{% extends 'finance/base.html' %}

{% block title %}Delete Expense - Finance Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>Are you sure you want to delete this expense record?</p>
                
                <div class="card bg-light">
                    <div class="card-body">
                        <h5 class="card-title">{{ expense.title }}</h5>
                        <p class="card-text">
                            <strong>Amount:</strong> ${{ expense.amount|floatformat:2 }}<br>
                            <strong>Date:</strong> {{ expense.date|date:"M d, Y" }}<br>
                            {% if expense.category %}
                                <strong>Category:</strong> {{ expense.category.name }}<br>
                            {% endif %}
                            {% if expense.description %}
                                <strong>Description:</strong> {{ expense.description|truncatewords:20 }}
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                <form method="post" class="mt-3">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'expense_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Expense
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
