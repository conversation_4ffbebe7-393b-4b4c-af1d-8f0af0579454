{% extends 'finance/base.html' %}

{% block title %}Dashboard - Finance Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Financial Dashboard
        </h1>
    </div>
</div>

{% if user.is_authenticated %}
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Income</h5>
                        <h3>${{ total_income|floatformat:2 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-plus-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Expenses</h5>
                        <h3>${{ total_expenses|floatformat:2 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-minus-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card {% if balance >= 0 %}bg-info{% else %}bg-warning{% endif %} text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Balance</h5>
                        <h3>${{ balance|floatformat:2 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-balance-scale fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle text-success me-2"></i>Recent Income
                </h5>
                <a href="{% url 'income_create' %}" class="btn btn-success btn-sm">
                    <i class="fas fa-plus me-1"></i>Add Income
                </a>
            </div>
            <div class="card-body">
                {% if recent_incomes %}
                    {% for income in recent_incomes %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <strong>{{ income.title }}</strong><br>
                            <small class="text-muted">{{ income.date }}</small>
                        </div>
                        <span class="text-success">${{ income.amount }}</span>
                    </div>
                    {% endfor %}
                    <div class="text-center mt-3">
                        <a href="{% url 'income_list' %}" class="btn btn-outline-primary btn-sm">View All</a>
                    </div>
                {% else %}
                    <p class="text-muted">No income records yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-minus-circle text-danger me-2"></i>Recent Expenses
                </h5>
                <a href="{% url 'expense_create' %}" class="btn btn-danger btn-sm">
                    <i class="fas fa-plus me-1"></i>Add Expense
                </a>
            </div>
            <div class="card-body">
                {% if recent_expenses %}
                    {% for expense in recent_expenses %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <strong>{{ expense.title }}</strong><br>
                            <small class="text-muted">{{ expense.date }}</small>
                        </div>
                        <span class="text-danger">${{ expense.amount }}</span>
                    </div>
                    {% endfor %}
                    <div class="text-center mt-3">
                        <a href="{% url 'expense_list' %}" class="btn btn-outline-primary btn-sm">View All</a>
                    </div>
                {% else %}
                    <p class="text-muted">No expense records yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="row">
    <div class="col-12 text-center">
        <div class="card">
            <div class="card-body">
                <h2>Welcome to Finance Manager</h2>
                <p class="lead">Track your income and expenses with ease.</p>
                <a href="{% url 'admin:login' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>Login to Get Started
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
