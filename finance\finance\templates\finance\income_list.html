{% extends 'finance/base.html' %}

{% block title %}Income Records - Finance Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus-circle text-success me-2"></i>Income Records
            </h1>
            <a href="{% url 'income_create' %}" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>Add New Income
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if incomes %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Amount</th>
                                <th>Category</th>
                                <th>Date</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for income in incomes %}
                            <tr>
                                <td><strong>{{ income.title }}</strong></td>
                                <td class="text-success">${{ income.amount|floatformat:2 }}</td>
                                <td>
                                    {% if income.category %}
                                        <span class="badge bg-secondary">{{ income.category.name }}</span>
                                    {% else %}
                                        <span class="text-muted">No category</span>
                                    {% endif %}
                                </td>
                                <td>{{ income.date|date:"M d, Y" }}</td>
                                <td>
                                    {% if income.description %}
                                        {{ income.description|truncatewords:10 }}
                                    {% else %}
                                        <span class="text-muted">No description</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'income_update' income.pk %}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'income_delete' income.pk %}" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="text-muted">Total Records: {{ incomes.count }}</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <strong>Total Income: ${{ incomes|length|add:0 }}</strong>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                    <h4>No Income Records Yet</h4>
                    <p class="text-muted">Start tracking your income by adding your first record.</p>
                    <a href="{% url 'income_create' %}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>Add Your First Income
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
