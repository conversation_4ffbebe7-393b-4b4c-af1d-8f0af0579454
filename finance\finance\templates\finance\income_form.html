{% extends 'finance/base.html' %}

{% block title %}{{ title }} - Finance Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle text-success me-2"></i>{{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">Title *</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger">
                                {% for error in form.title.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.amount.id_for_label }}" class="form-label">Amount *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            {{ form.amount }}
                        </div>
                        {% if form.amount.errors %}
                            <div class="text-danger">
                                {% for error in form.amount.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                        {{ form.category }}
                        {% if form.category.errors %}
                            <div class="text-danger">
                                {% for error in form.category.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            <a href="{% url 'category_create' %}" target="_blank">Create new category</a>
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.date.id_for_label }}" class="form-label">Date *</label>
                        {{ form.date }}
                        {% if form.date.errors %}
                            <div class="text-danger">
                                {% for error in form.date.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'income_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>Save Income
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
