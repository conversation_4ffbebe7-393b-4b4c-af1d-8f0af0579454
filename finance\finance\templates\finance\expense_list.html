{% extends 'finance/base.html' %}

{% block title %}Expense Records - Finance Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-minus-circle text-danger me-2"></i>Expense Records
            </h1>
            <a href="{% url 'expense_create' %}" class="btn btn-danger">
                <i class="fas fa-plus me-1"></i>Add New Expense
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if expenses %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Amount</th>
                                <th>Category</th>
                                <th>Date</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for expense in expenses %}
                            <tr>
                                <td><strong>{{ expense.title }}</strong></td>
                                <td class="text-danger">${{ expense.amount|floatformat:2 }}</td>
                                <td>
                                    {% if expense.category %}
                                        <span class="badge bg-secondary">{{ expense.category.name }}</span>
                                    {% else %}
                                        <span class="text-muted">No category</span>
                                    {% endif %}
                                </td>
                                <td>{{ expense.date|date:"M d, Y" }}</td>
                                <td>
                                    {% if expense.description %}
                                        {{ expense.description|truncatewords:10 }}
                                    {% else %}
                                        <span class="text-muted">No description</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'expense_update' expense.pk %}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'expense_delete' expense.pk %}" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="text-muted">Total Records: {{ expenses.count }}</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <strong>Total Expenses: ${{ expenses|length|add:0 }}</strong>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-minus-circle fa-3x text-muted mb-3"></i>
                    <h4>No Expense Records Yet</h4>
                    <p class="text-muted">Start tracking your expenses by adding your first record.</p>
                    <a href="{% url 'expense_create' %}" class="btn btn-danger">
                        <i class="fas fa-plus me-1"></i>Add Your First Expense
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
