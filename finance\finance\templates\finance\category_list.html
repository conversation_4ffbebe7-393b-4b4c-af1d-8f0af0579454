{% extends 'finance/base.html' %}

{% block title %}Categories - Finance Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-tags text-info me-2"></i>Categories
            </h1>
            <a href="{% url 'category_create' %}" class="btn btn-info">
                <i class="fas fa-plus me-1"></i>Add New Category
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if categories %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category in categories %}
                            <tr>
                                <td><strong>{{ category.name }}</strong></td>
                                <td>
                                    {% if category.description %}
                                        {{ category.description|truncatewords:15 }}
                                    {% else %}
                                        <span class="text-muted">No description</span>
                                    {% endif %}
                                </td>
                                <td>{{ category.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h4>No Categories Yet</h4>
                    <p class="text-muted">Create categories to organize your income and expenses.</p>
                    <a href="{% url 'category_create' %}" class="btn btn-info">
                        <i class="fas fa-plus me-1"></i>Create Your First Category
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
