from django.contrib import admin
from .models import Category, Income, Expense

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at']
    search_fields = ['name']
    list_filter = ['created_at']

@admin.register(Income)
class IncomeAdmin(admin.ModelAdmin):
    list_display = ['title', 'amount', 'category', 'user', 'date', 'created_at']
    list_filter = ['category', 'date', 'created_at', 'user']
    search_fields = ['title', 'description']
    date_hierarchy = 'date'
    list_per_page = 20

@admin.register(Expense)
class ExpenseAdmin(admin.ModelAdmin):
    list_display = ['title', 'amount', 'category', 'user', 'date', 'created_at']
    list_filter = ['category', 'date', 'created_at', 'user']
    search_fields = ['title', 'description']
    date_hierarchy = 'date'
    list_per_page = 20
