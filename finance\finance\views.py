from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum
from django.utils import timezone
from .models import Income, Expense, Category
from .forms import IncomeForm, ExpenseForm, CategoryForm

def dashboard(request):
    """Dashboard view showing summary of income and expenses"""
    if request.user.is_authenticated:
        # Get user's financial data
        total_income = Income.objects.filter(user=request.user).aggregate(Sum('amount'))['amount__sum'] or 0
        total_expenses = Expense.objects.filter(user=request.user).aggregate(Sum('amount'))['amount__sum'] or 0
        balance = total_income - total_expenses

        # Recent transactions
        recent_incomes = Income.objects.filter(user=request.user)[:5]
        recent_expenses = Expense.objects.filter(user=request.user)[:5]

        context = {
            'total_income': total_income,
            'total_expenses': total_expenses,
            'balance': balance,
            'recent_incomes': recent_incomes,
            'recent_expenses': recent_expenses,
        }
    else:
        context = {}

    return render(request, 'finance/dashboard.html', context)

# Income Views
@login_required
def income_list(request):
    """List all incomes for the current user"""
    incomes = Income.objects.filter(user=request.user)
    return render(request, 'finance/income_list.html', {'incomes': incomes})

@login_required
def income_create(request):
    """Create a new income record"""
    if request.method == 'POST':
        form = IncomeForm(request.POST)
        if form.is_valid():
            income = form.save(commit=False)
            income.user = request.user
            income.save()
            messages.success(request, 'Income record created successfully!')
            return redirect('income_list')
    else:
        form = IncomeForm()
    return render(request, 'finance/income_form.html', {'form': form, 'title': 'Add Income'})

@login_required
def income_update(request, pk):
    """Update an existing income record"""
    income = get_object_or_404(Income, pk=pk, user=request.user)
    if request.method == 'POST':
        form = IncomeForm(request.POST, instance=income)
        if form.is_valid():
            form.save()
            messages.success(request, 'Income record updated successfully!')
            return redirect('income_list')
    else:
        form = IncomeForm(instance=income)
    return render(request, 'finance/income_form.html', {'form': form, 'title': 'Edit Income'})

@login_required
def income_delete(request, pk):
    """Delete an income record"""
    income = get_object_or_404(Income, pk=pk, user=request.user)
    if request.method == 'POST':
        income.delete()
        messages.success(request, 'Income record deleted successfully!')
        return redirect('income_list')
    return render(request, 'finance/income_confirm_delete.html', {'income': income})

# Expense Views
@login_required
def expense_list(request):
    """List all expenses for the current user"""
    expenses = Expense.objects.filter(user=request.user)
    return render(request, 'finance/expense_list.html', {'expenses': expenses})

@login_required
def expense_create(request):
    """Create a new expense record"""
    if request.method == 'POST':
        form = ExpenseForm(request.POST)
        if form.is_valid():
            expense = form.save(commit=False)
            expense.user = request.user
            expense.save()
            messages.success(request, 'Expense record created successfully!')
            return redirect('expense_list')
    else:
        form = ExpenseForm()
    return render(request, 'finance/expense_form.html', {'form': form, 'title': 'Add Expense'})

@login_required
def expense_update(request, pk):
    """Update an existing expense record"""
    expense = get_object_or_404(Expense, pk=pk, user=request.user)
    if request.method == 'POST':
        form = ExpenseForm(request.POST, instance=expense)
        if form.is_valid():
            form.save()
            messages.success(request, 'Expense record updated successfully!')
            return redirect('expense_list')
    else:
        form = ExpenseForm(instance=expense)
    return render(request, 'finance/expense_form.html', {'form': form, 'title': 'Edit Expense'})

@login_required
def expense_delete(request, pk):
    """Delete an expense record"""
    expense = get_object_or_404(Expense, pk=pk, user=request.user)
    if request.method == 'POST':
        expense.delete()
        messages.success(request, 'Expense record deleted successfully!')
        return redirect('expense_list')
    return render(request, 'finance/expense_confirm_delete.html', {'expense': expense})

# Category Views
@login_required
def category_list(request):
    """List all categories"""
    categories = Category.objects.all()
    return render(request, 'finance/category_list.html', {'categories': categories})

@login_required
def category_create(request):
    """Create a new category"""
    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Category created successfully!')
            return redirect('category_list')
    else:
        form = CategoryForm()
    return render(request, 'finance/category_form.html', {'form': form, 'title': 'Add Category'})
